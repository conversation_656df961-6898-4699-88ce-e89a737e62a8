const {validatePhone} = require('../../../utils/validator'); 
const {uploadFile} = require('../../../utils/upload');
const {getUserInfo, setUserInfo} = require('../../../utils/storage');
const {getVerificationStatus, submitAttendantVerification, validateReferrer} = require('../../../services/attendant');
const {getUserProfile} = require('../../../services/user');

Page({
  data: {
    form: {
      avatar: '',
      name: '',
      gender: '',
      age: '',
      phone: '',
      idCard: '',
      idCardFront: '',
      idCardBack: '',
      healthCert: '',
      experience: '',
      region: [],
      introduction: '',
      referrerPhone: ''
    },
    experienceOptions: ['1年以下', '1-3年', '3-5年', '5年以上'],
    experienceIndex: -1,
    uploadStatus: {
      avatar: false,
      idCardFront: false,
      idCardBack: false,
      healthCert: false
    },
    userAvatar: '',
    userName: '',
    userPhone: '',
    isEditing: false,
    verificationId: 0,
    defaultRegion: ['北京市', '北京市'],
    referrerValidation: {
      validating: false,
      success: false,
      error: false,
      errorMessage: '',
      referrerName: ''
    }
  },

  onLoad() {
    console.log('[Register] 页面加载，开始获取用户信息');
    
    // 先从后端获取最新用户信息
    this.fetchUserProfile();
    
    // 检查是否有现有的认证申请
    this.checkExistingVerification();
  },

  onShow() {
    console.log('[Register] 页面显示');
  },
  
  // 从后端获取最新用户信息
  fetchUserProfile() {
    wx.showLoading({
      title: '加载信息...'
    });
    
    // 调用后端API获取最新用户信息
    getUserProfile()
      .then(res => {
        wx.hideLoading();
        console.log('[Register] 获取用户信息成功:', res);
        
        if (res.code === 0 && res.data) {
          const userData = res.data;
          
          // 更新本地存储
          setUserInfo(userData);
          
          // 更新页面数据
          this.setData({
            userAvatar: userData.avatar_url || userData.avatar || '',
            userName: userData.nickname || '',
            userPhone: userData.phone || '',
            'form.phone': userData.phone || ''
          });
          
          console.log('[Register] 用户信息已更新 - 头像:', this.data.userAvatar, '昵称:', this.data.userName);
        } else {
          console.warn('[Register] 获取用户信息返回异常结果:', res);
          this.loadLocalUserInfo();
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('[Register] 获取用户信息失败:', err);
        this.loadLocalUserInfo();
      });
  },
  
  // 从本地缓存加载用户信息作为备选
  loadLocalUserInfo() {
    console.log('[Register] 从本地缓存加载用户信息');
    const userInfo = getUserInfo();
    if (userInfo) {
      if (userInfo.avatar || userInfo.avatar_url) {
        const avatarUrl = userInfo.avatar || userInfo.avatar_url;
        this.setData({
          userAvatar: avatarUrl
        });
      }

      if (userInfo.nickname) {
        this.setData({
          userName: userInfo.nickname
        });
      }

      if (userInfo.phone) {
        this.setData({
          userPhone: userInfo.phone,
          'form.phone': userInfo.phone
        });
      }
    }
  },
  
  // 检查现有的认证申请
  checkExistingVerification() {
    wx.showLoading({
      title: '加载中...'
    });
    
    getVerificationStatus()
      .then(res => {
        wx.hideLoading();
        console.log('[Register] 获取认证信息:', res);
        
        // 如果有认证记录，尤其是被拒绝的
        if (res.code === 0 && res.data) {
          const verification = res.data;
          
          // 设置编辑模式标记
          this.setData({
            isEditing: true,
            verificationId: verification.id
          });
          
          // 先清理数据，确保不会有冲突
          let gender = '';
          if (verification.gender) {
            gender = String(verification.gender);
          }
          
          // 处理工作经验
          let expText = '';
          let expIndex = -1;
          
          if (typeof verification.experience === 'number') {
            console.log('[Register] 工作经验为数字:', verification.experience);
            switch(verification.experience) {
              case 0:
                expText = '1年以下';
                expIndex = 0;
                break;
              case 1:
                expText = '1-3年';
                expIndex = 1;
                break;
              case 2:
                expText = '3-5年';
                expIndex = 2;
                break;
              case 3:
                expText = '5年以上';
                expIndex = 3;
                break;
              default:
                expText = '';
                expIndex = -1;
                break;
            }
          } else if (typeof verification.experience === 'string') {
            expText = verification.experience;
            expIndex = this.data.experienceOptions.indexOf(verification.experience);
            if (expIndex === -1) expIndex = 0;
          }
          
          // 处理服务区域数据
          let regionArray = [];
          if (verification.service_area) {
            if (typeof verification.service_area === 'string') {
              regionArray = verification.service_area.split(',');
            } else if (Array.isArray(verification.service_area)) {
              regionArray = verification.service_area;
            }
          }
          
          // 确保regionArray至少有两个元素
          if (regionArray.length < 2) {
            regionArray = this.data.defaultRegion;
          }
          
          // 填充表单数据
          this.setData({
            'form.avatar': verification.avatar || '',
            'form.name': verification.name || '',
            'form.gender': gender,
            'form.age': verification.age ? String(verification.age) : '',
            'form.phone': verification.phone || '',
            'form.idCard': verification.id_card || '',
            'form.idCardFront': verification.id_card_front || '',
            'form.idCardBack': verification.id_card_back || '',
            'form.healthCert': verification.health_cert || '',
            'form.experience': expText,
            'form.region': regionArray,
            'form.introduction': verification.introduction || '',
            'form.referrerPhone': verification.referrer_phone || '',
            experienceIndex: expIndex
          });
          
          // 如果有推荐人手机号，自动验证
          if (verification.referrer_phone) {
            this.validateReferrerAPI(verification.referrer_phone);
          }
          
          console.log('[Register] 表单数据已填充 - 编辑模式');
          console.log('  经验原始值:', verification.experience);
          console.log('  转换后值:', expText, '索引:', expIndex);
          console.log('  服务区域:', regionArray);
        } else {
          console.log('[Register] 没有现有认证申请 - 新建模式');
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('[Register] 获取认证状态失败:', err);
      });
  },

  // 使用微信头像
  useCurrentAvatar() {
    if (this.data.userAvatar) {
      this.setData({
        'form.avatar': this.data.userAvatar
      });
      
      wx.showToast({
        title: '头像已设置',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未获取到头像',
        icon: 'none'
      });
    }
  },

  // 使用微信昵称
  useCurrentName() {
    if (this.data.userName) {
      this.setData({
        'form.name': this.data.userName
      });
      
      wx.showToast({
        title: '姓名已设置',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未获取到昵称',
        icon: 'none'
      });
    }
  },

  // 选择头像
  async chooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中...'
        })
        this.setData({
          'uploadStatus.avatar': true
        })
        
        // 获取系统信息判断是否为iOS设备
        const systemInfo = wx.getSystemInfoSync()
        const isIOS = systemInfo.platform === 'ios'
        
        // 先设置临时图片路径进行预览（iOS兼容处理）
        let tempImagePath = res.tempFilePaths[0]
        
        if (isIOS) {
          // iOS设备需要特殊处理图片显示
          try {
            // 使用getLocalImgData获取base64数据（iOS WKWebview兼容）
            const localImgResult = await new Promise((resolve, reject) => {
              wx.getLocalImgData({
                localId: res.localIds ? res.localIds[0] : res.tempFilePaths[0],
                success: resolve,
                fail: reject
              })
            })
            
            if (localImgResult && localImgResult.localData) {
              // 使用base64数据显示图片
              tempImagePath = localImgResult.localData
            }
          } catch (localImgError) {
            console.log('[Register] iOS头像数据获取失败，使用原始路径:', localImgError)
            // 如果获取失败，仍使用原始路径
          }
        }
        
        // 先显示预览图片
        this.setData({
          'form.avatar': tempImagePath
        })
        
        try {
          // 上传图片到服务器
          const url = await uploadFile(res.tempFilePaths[0])
          console.log('[Register] 头像上传成功:', url);
          
          // 设置服务器返回的URL
          this.setData({
            'form.avatar': url,
            'uploadStatus.avatar': false
          })
        } catch (err) {
          console.error('[Register] 头像上传失败:', err)
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
          this.setData({
            'uploadStatus.avatar': false,
            'form.avatar': '' // 清空失败的图片
          })
        } finally {
          wx.hideLoading()
        }
      }
    })
  },

  // 输入姓名
  inputName(e) {
    this.setData({
      'form.name': e.detail.value
    })
  },

  // 选择性别
  selectGender(e) {
    this.setData({
      'form.gender': e.detail.value
    })
  },

  // 输入年龄
  inputAge(e) {
    this.setData({
      'form.age': e.detail.value
    })
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      'form.phone': e.detail.value
    })
  },

  // 输入身份证号码
  inputIdCard(e) {
    this.setData({
      'form.idCard': e.detail.value
    });
  },

  // 选择身份证正面照片
  chooseIdCardFront() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中...'
        })
        this.setData({
          'uploadStatus.idCardFront': true
        })
        
        try {
          // 获取系统信息判断是否为iOS设备
          const systemInfo = wx.getSystemInfoSync()
          const isIOS = systemInfo.platform === 'ios'
          
          // 先设置临时图片路径进行预览（iOS兼容处理）
          let tempImagePath = res.tempFilePaths[0]
          
          if (isIOS) {
            // iOS设备需要特殊处理图片显示
            try {
              // 使用getLocalImgData获取base64数据（iOS WKWebview兼容）
              const localImgResult = await new Promise((resolve, reject) => {
                wx.getLocalImgData({
                  localId: res.localIds ? res.localIds[0] : res.tempFilePaths[0],
                  success: resolve,
                  fail: reject
                })
              })
              
              if (localImgResult && localImgResult.localData) {
                // 使用base64数据显示图片
                tempImagePath = localImgResult.localData
              }
            } catch (localImgError) {
              console.log('[Register] iOS图片数据获取失败，使用原始路径:', localImgError)
              // 如果获取失败，仍使用原始路径
            }
          }
          
          // 先显示预览图片
          this.setData({
            'form.idCardFront': tempImagePath
          })
          
          // 上传图片到服务器
          const url = await uploadFile(res.tempFilePaths[0])
          console.log('[Register] 身份证正面上传成功:', url);
          
          // 设置服务器返回的URL
          this.setData({
            'form.idCardFront': url,
            'uploadStatus.idCardFront': false
          })

          wx.hideLoading()
          
        } catch (err) {
          console.error('[Register] 身份证正面上传失败:', err)
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
          this.setData({
            'uploadStatus.idCardFront': false,
            'form.idCardFront': '' // 清空失败的图片
          })
          wx.hideLoading()
        }
      }
    })
  },

  // 选择身份证反面照片
  chooseIdCardBack() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中...'
        })
        this.setData({
          'uploadStatus.idCardBack': true
        })
        
        try {
          // 获取系统信息判断是否为iOS设备
          const systemInfo = wx.getSystemInfoSync()
          const isIOS = systemInfo.platform === 'ios'
          
          // 先设置临时图片路径进行预览（iOS兼容处理）
          let tempImagePath = res.tempFilePaths[0]
          
          if (isIOS) {
            // iOS设备需要特殊处理图片显示
            try {
              // 使用getLocalImgData获取base64数据（iOS WKWebview兼容）
              const localImgResult = await new Promise((resolve, reject) => {
                wx.getLocalImgData({
                  localId: res.localIds ? res.localIds[0] : res.tempFilePaths[0],
                  success: resolve,
                  fail: reject
                })
              })
              
              if (localImgResult && localImgResult.localData) {
                // 使用base64数据显示图片
                tempImagePath = localImgResult.localData
              }
            } catch (localImgError) {
              console.log('[Register] iOS图片数据获取失败，使用原始路径:', localImgError)
              // 如果获取失败，仍使用原始路径
            }
          }
          
          // 先显示预览图片
          this.setData({
            'form.idCardBack': tempImagePath
          })
          
          // 上传图片到服务器
          const url = await uploadFile(res.tempFilePaths[0])
          console.log('[Register] 身份证反面上传成功:', url);
          
          // 设置服务器返回的URL
          this.setData({
            'form.idCardBack': url,
            'uploadStatus.idCardBack': false
          })

          wx.hideLoading()
          
        } catch (err) {
          console.error('[Register] 身份证反面上传失败:', err)
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
          this.setData({
            'uploadStatus.idCardBack': false,
            'form.idCardBack': '' // 清空失败的图片
          })
          wx.hideLoading()
        }
      }
    })
  },

  // 选择健康证照片
  chooseHealthCert() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中...'
        })
        this.setData({
          'uploadStatus.healthCert': true
        })
        
        try {
          // 获取系统信息判断是否为iOS设备
          const systemInfo = wx.getSystemInfoSync()
          const isIOS = systemInfo.platform === 'ios'
          
          // 先设置临时图片路径进行预览（iOS兼容处理）
          let tempImagePath = res.tempFilePaths[0]
          
          if (isIOS) {
            // iOS设备需要特殊处理图片显示
            try {
              // 使用getLocalImgData获取base64数据（iOS WKWebview兼容）
              const localImgResult = await new Promise((resolve, reject) => {
                wx.getLocalImgData({
                  localId: res.localIds ? res.localIds[0] : res.tempFilePaths[0],
                  success: resolve,
                  fail: reject
                })
              })
              
              if (localImgResult && localImgResult.localData) {
                // 使用base64数据显示图片
                tempImagePath = localImgResult.localData
              }
            } catch (localImgError) {
              console.log('[Register] iOS健康证数据获取失败，使用原始路径:', localImgError)
              // 如果获取失败，仍使用原始路径
            }
          }
          
          // 先显示预览图片
          this.setData({
            'form.healthCert': tempImagePath
          })
          
          // 上传图片到服务器
          const url = await uploadFile(res.tempFilePaths[0])
          console.log('[Register] 健康证上传成功:', url);
          
          // 设置服务器返回的URL
          this.setData({
            'form.healthCert': url,
            'uploadStatus.healthCert': false
          })
          
        } catch (err) {
          console.error('[Register] 健康证上传失败:', err)
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
          this.setData({
            'uploadStatus.healthCert': false,
            'form.healthCert': '' // 清空失败的图片
          })
        } finally {
          wx.hideLoading()
        }
      }
    })
  },

  // 选择工作经验
  selectExperience(e) {
    this.setData({
      experienceIndex: e.detail.value,
      'form.experience': this.data.experienceOptions[e.detail.value]
    })
  },

  // 选择服务区域
  selectRegion(e) {
    console.log('[Register] 选择服务区域:', e.detail.value);
    
    // 只保存省市信息（前两级）
    const regionValue = e.detail.value;
    if (regionValue && regionValue.length >= 2) {
      this.setData({
        'form.region': [regionValue[0], regionValue[1]]
      });
      console.log('[Register] 服务区域已设置:', this.data.form.region);
    } else {
      console.warn('[Register] 区域选择数据不完整:', regionValue);
    }
  },

  // 输入个人简介
  inputIntroduction(e) {
    this.setData({
      'form.introduction': e.detail.value
    })
  },

  // 输入推荐人手机号
  inputReferrerPhone(e) {
    const phone = e.detail.value
    this.setData({
      'form.referrerPhone': phone,
      'referrerValidation.success': false,
      'referrerValidation.error': false,
      'referrerValidation.errorMessage': '',
      'referrerValidation.referrerName': ''
    })
  },

  // 验证推荐人
  validateReferrer(e) {
    const phone = e.detail.value.trim()
    
    // 如果手机号为空，清除验证状态
    if (!phone) {
      this.setData({
        'referrerValidation.validating': false,
        'referrerValidation.success': false,
        'referrerValidation.error': false,
        'referrerValidation.errorMessage': '',
        'referrerValidation.referrerName': ''
      })
      return
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      this.setData({
        'referrerValidation.validating': false,
        'referrerValidation.success': false,
        'referrerValidation.error': true,
        'referrerValidation.errorMessage': '手机号格式不正确',
        'referrerValidation.referrerName': ''
      })
      return
    }

    // 开始验证
    this.setData({
      'referrerValidation.validating': true,
      'referrerValidation.success': false,
      'referrerValidation.error': false,
      'referrerValidation.errorMessage': '',
      'referrerValidation.referrerName': ''
    })

    // 调用验证API
    this.validateReferrerAPI(phone)
  },

  // 调用推荐人验证API
  validateReferrerAPI(phone) {
    // 防抖处理
    if (this.validateReferrerTimer) {
      clearTimeout(this.validateReferrerTimer)
    }

    this.validateReferrerTimer = setTimeout(() => {
      validateReferrer(phone)
        .then(res => {
          console.log('[Register] 推荐人验证结果:', res)
          
          if (res.code === 0 && res.data) {
            const result = res.data
            if (result.valid) {
              this.setData({
                'referrerValidation.validating': false,
                'referrerValidation.success': true,
                'referrerValidation.error': false,
                'referrerValidation.errorMessage': '',
                'referrerValidation.referrerName': result.referrer_name || '推荐人'
              })
            } else {
              this.setData({
                'referrerValidation.validating': false,
                'referrerValidation.success': false,
                'referrerValidation.error': true,
                'referrerValidation.errorMessage': '未发现该手机号陪诊师',
                'referrerValidation.referrerName': ''
              })
            }
          } else {
            this.setData({
              'referrerValidation.validating': false,
              'referrerValidation.success': false,
              'referrerValidation.error': true,
              'referrerValidation.errorMessage': res.message || '验证失败',
              'referrerValidation.referrerName': ''
            })
          }
        })
        .catch(err => {
          console.error('[Register] 推荐人验证失败:', err)
          
          // 根据错误类型提供不同的错误信息
          let errorMessage = '网络错误，请重试'
          if (err.message) {
            if (err.message.includes('手机号格式')) {
              errorMessage = '手机号格式不正确'
            } else if (err.message.includes('未发现')) {
              errorMessage = '未发现该手机号陪诊师'
            } else if (err.message.includes('未认证')) {
              errorMessage = '推荐人未认证'
            } else {
              errorMessage = err.message
            }
          }
          
          this.setData({
            'referrerValidation.validating': false,
            'referrerValidation.success': false,
            'referrerValidation.error': true,
            'referrerValidation.errorMessage': errorMessage,
            'referrerValidation.referrerName': ''
          })
        })
    }, 500) // 500ms防抖
  },

  // 重试验证推荐人
  retryValidateReferrer() {
    const phone = this.data.form.referrerPhone
    if (phone) {
      this.validateReferrerAPI(phone)
    }
  },

  // 验证表单
  validateForm() {
    const { form } = this.data
    if (!form.avatar) {
      wx.showToast({
        title: '请上传头像',
        icon: 'none'
      })
      return false
    }
    if (!form.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return false
    }
    if (!form.gender) {
      wx.showToast({
        title: '请选择性别',
        icon: 'none'
      })
      return false
    }
    if (!form.age) {
      wx.showToast({
        title: '请输入年龄',
        icon: 'none'
      })
      return false
    }
    if (!form.phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }
    if (!/^1[3-9]\d{9}$/.test(form.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!form.idCardFront || !form.idCardBack) {
      wx.showToast({
        title: '请上传身份证照片',
        icon: 'none'
      })
      return false
    }
    if (!form.idCard) {
      wx.showToast({
        title: '请输入身份证号码',
        icon: 'none'
      })
      return false
    }
    if (!/^\d{17}[\dX]$/.test(form.idCard)) {
      wx.showToast({
        title: '身份证号码格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!form.experience) {
      wx.showToast({
        title: '请选择工作经验',
        icon: 'none'
      })
      return false
    }
    if (!form.region || form.region.length < 2) {
      wx.showToast({
        title: '请选择服务区域',
        icon: 'none'
      })
      return false
    }
    if (!form.introduction) {
      wx.showToast({
        title: '请填写个人简介',
        icon: 'none'
      })
      return false
    }
    if (form.introduction.length < 10) {
      wx.showToast({
        title: '个人简介至少10个字符',
        icon: 'none'
      })
      return false
    }
    
    // 验证推荐人字段（如果填写了）
    if (form.referrerPhone && form.referrerPhone.trim() !== '') {
      // 检查推荐人验证状态
      if (this.data.referrerValidation.validating) {
        wx.showToast({
          title: '推荐人验证中，请稍候',
          icon: 'none'
        })
        return false
      }
      
      if (this.data.referrerValidation.error) {
        wx.showToast({
          title: '请修正推荐人信息或清空该字段',
          icon: 'none'
        })
        return false
      }
      
      if (!this.data.referrerValidation.success) {
        wx.showToast({
          title: '请验证推荐人信息',
          icon: 'none'
        })
        return false
      }
    }
    
    return true
  },

  // 提交表单
  submitForm() {
    if (!this.validateForm()) return

    wx.showLoading({
      title: '提交中...'
    })
    
    console.log('[Register] 提交表单数据:', this.data.form);
    
    const submitData = {
      avatar: this.data.form.avatar,
      name: this.data.form.name,
      gender: parseInt(this.data.form.gender),
      age: parseInt(this.data.form.age),
      phone: this.data.form.phone,
      id_card: this.data.form.idCard,
      id_card_front: this.data.form.idCardFront,
      id_card_back: this.data.form.idCardBack,
      health_cert: this.data.form.healthCert || '',
      experience: this.data.form.experience,
      service_area: this.data.form.region.join(','),
      introduction: this.data.form.introduction,
      referrer_phone: this.data.form.referrerPhone || ''
    };

    // 如果是编辑模式，添加ID
    if (this.data.isEditing && this.data.verificationId) {
      submitData.id = this.data.verificationId;
    }

    console.log('[Register] 最终提交数据:', submitData);

    submitAttendantVerification(submitData)
      .then(res => {
        wx.hideLoading()
        console.log('[Register] 提交成功:', res);
        
        if (res.code === 0) {
          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          })
          
          setTimeout(() => {
            wx.navigateBack()
          }, 2000)
        } else {
          wx.showToast({
            title: res.message || '提交失败',
            icon: 'none'
          })
        }
      })
      .catch(err => {
        wx.hideLoading()
        console.error('[Register] 提交失败:', err);
        wx.showToast({
          title: '提交失败：' + (err.message || '网络错误'),
          icon: 'none'
        })
      })
  }
})