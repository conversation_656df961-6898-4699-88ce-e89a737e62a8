<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">成为陪诊师</text>
    <text class="subtitle">填写资料，开启服务之旅</text>
  </view>

  <!-- 表单 -->
  <view class="form">
    <!-- 基本信息 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">基本信息</text>
        <text class="title-required">*</text>
      </view>

      <!-- 头像 -->
      <view class="form-item">
        <view class="label required">头像</view>
        <view class="avatar-upload" bindtap="chooseAvatar">
          <image wx:if="{{form.avatar}}" src="{{form.avatar}}" mode="aspectFill"></image>
          <view wx:else class="upload-placeholder">
            <text class="icon">+</text>
            <text>上传头像</text>
          </view>
          <view wx:if="{{uploadStatus.avatar}}" class="uploading">
            <text>上传中...</text>
          </view>
        </view>
        <view class="name-actions">
          <text class="action-btn" bindtap="useCurrentAvatar">使用当前头像</text>
        </view>
      </view>

      <!-- 姓名 -->
      <view class="form-item">
        <view class="label required">姓名</view>
        <input 
          type="text" 
          placeholder="请输入真实姓名" 
          value="{{form.name}}" 
          bindinput="inputName"
          maxlength="10"
        />
        <view class="name-actions">
          <text class="action-btn" bindtap="useCurrentName">使用当前昵称</text>
        </view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="label required">性别</view>
        <radio-group bindchange="selectGender">
          <label class="radio-item">
            <radio value="1" checked="{{form.gender == 1}}" />
            <text>男</text>
          </label>
          <label class="radio-item">
            <radio value="2" checked="{{form.gender == 2}}" />
            <text>女</text>
          </label>
        </radio-group>
      </view>

      <!-- 年龄 -->
      <view class="form-item">
        <view class="label required">年龄</view>
        <input 
          type="number" 
          placeholder="请输入年龄" 
          value="{{form.age}}" 
          bindinput="inputAge"
          maxlength="2"
        />
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <view class="label required">手机号</view>
        <input 
          type="number" 
          placeholder="请输入手机号" 
          value="{{form.phone}}" 
          bindinput="inputPhone"
          maxlength="11"
        />
      </view>

      <!-- 推荐人手机号 -->
      <view class="form-item">
        <view class="label">推荐人手机号（选填）</view>
        <input 
          type="number" 
          placeholder="请输入推荐人手机号" 
          value="{{form.referrerPhone}}" 
          bindinput="inputReferrerPhone"
          bindblur="validateReferrer"
          maxlength="11"
        />
        <view wx:if="{{referrerValidation.validating}}" class="validation-status validating">
          <text class="icon">⏳</text>
          <text>验证中...</text>
        </view>
        <view wx:if="{{referrerValidation.error}}" class="validation-status error">
          <text class="icon">❌</text>
          <text>{{referrerValidation.errorMessage}}</text>
          <text class="retry-btn" bindtap="retryValidateReferrer">重试</text>
        </view>
        <view wx:if="{{referrerValidation.success}}" class="validation-status success">
          <text class="icon">✅</text>
          <text>推荐人验证通过：{{referrerValidation.referrerName}}</text>
        </view>
      </view>
    </view>

    <!-- 资质信息 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">资质信息</text>
        <text class="title-required">*</text>
      </view>

      <!-- 身份证照片 -->
      <view class="form-item">
        <view class="label required">身份证照片</view>
        <view class="id-card-upload">
          <view class="upload-item" bindtap="chooseIdCardFront">
            <image wx:if="{{form.idCardFront}}" src="{{form.idCardFront}}" mode="aspectFill"></image>
            <view wx:else class="upload-placeholder">
              <text class="icon">+</text>
              <text>身份证正面</text>
            </view>
            <view wx:if="{{uploadStatus.idCardFront}}" class="uploading">
              <text>上传中...</text>
            </view>
          </view>
          <view class="upload-item" bindtap="chooseIdCardBack">
            <image wx:if="{{form.idCardBack}}" src="{{form.idCardBack}}" mode="aspectFill"></image>
            <view wx:else class="upload-placeholder">
              <text class="icon">+</text>
              <text>身份证反面</text>
            </view>
            <view wx:if="{{uploadStatus.idCardBack}}" class="uploading">
              <text>上传中...</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 身份证号码 -->
      <view class="form-item">
        <view class="label required">身份证号码</view>
        <input 
          type="text" 
          placeholder="请输入18位身份证号码" 
          value="{{form.idCard}}" 
          bindinput="inputIdCard"
          maxlength="18"
        />
      </view>

      <!-- 健康证 -->
      <view class="form-item">
        <view class="label">健康证</view>
        <view class="upload-item" bindtap="chooseHealthCert">
          <image wx:if="{{form.healthCert}}" src="{{form.healthCert}}" mode="aspectFill"></image>
          <view wx:else class="upload-placeholder">
            <text class="icon">+</text>
            <text>上传健康证照片（可选）</text>
          </view>
          <view wx:if="{{uploadStatus.healthCert}}" class="uploading">
            <text>上传中...</text>
          </view>
        </view>
      </view>

      <!-- 工作经验 -->
      <view class="form-item">
        <view class="label required">工作经验</view>
        <picker bindchange="selectExperience" value="{{experienceIndex}}" range="{{experienceOptions}}">
          <view class="picker">
            {{experienceIndex === -1 ? '请选择工作经验' : experienceOptions[experienceIndex]}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 服务信息 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">服务信息</text>
        <text class="title-required">*</text>
      </view>

      <!-- 服务区域 -->
      <view class="form-item">
        <view class="label required">服务区域</view>
        <picker 
          mode="region" 
          bindchange="selectRegion" 
          value="{{form.region.length > 0 ? form.region : defaultRegion}}"
          level="city"
        >
          <view class="picker {{form.region.length > 0 ? 'picker-selected' : ''}}">
            {{form.region.length > 0 ? form.region[0] + ' - ' + form.region[1] : '请选择服务区域'}}
          </view>
        </picker>
      </view>

      <!-- 个人简介 -->
      <view class="form-item">
        <view class="label required">个人简介</view>
        <textarea 
          placeholder="请简单介绍一下您的工作经历、服务优势等，让用户更好地了解您"
          value="{{form.introduction}}" 
          bindinput="inputIntroduction"
          maxlength="200"
        ></textarea>
        <view class="char-count">{{form.introduction.length}}/200</view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn" bindtap="submitForm">提交申请</button>
    <view class="submit-tip">
      <text>提交后我们将在1-3个工作日内完成审核</text>
    </view>
  </view>
</view>

<!-- 身份证信息确认组件 -->
<id-card-confirm 
  visible="{{showIdCardConfirm}}"
  id-card-info="{{recognizedIdCardInfo}}"
  bind:confirm="onIdCardConfirm"
  bind:cancel="onIdCardCancel"
  bind:close="onIdCardModalClose"
/>
