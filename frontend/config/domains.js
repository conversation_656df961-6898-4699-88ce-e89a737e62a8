// 前端域名配置统一管理文件
// 所有域名相关配置都在此文件中定义

const DOMAINS = {
  // 生产环境域名配置
  production: {
    mainDomain: 'kanghuxing.cn',
    apiDomain: 'www.kanghuxing.cn',
    adminDomain: 'www.kanghuxing.cn',
    miniappDomain: 'www.kanghuxing.cn',
    urls: {
      apiBase: 'https://www.kanghuxing.cn',
      adminBase: 'https://www.kanghuxing.cn/admin',
      miniappBase: 'https://www.kanghuxing.cn',
      uploadDomain: 'https://www.kanghuxing.cn'
    }
  },

  // 开发环境域名配置
  development: {
    mainDomain: 'localhost',
    apiDomain: 'localhost',
    adminDomain: 'localhost',
    miniappDomain: '127.0.0.1',
    urls: {
      apiBase: 'http://127.0.0.1:8080',
      adminBase: 'http://localhost:8081',
      frontendBase: 'http://localhost:3001',
      uploadDomain: 'http://127.0.0.1:8080'
    }
  }
}

// 端口配置
const PORTS = {
  backend: 8080,
  adminServer: 8081,
  adminWeb: 3001,
  mysql: 3306,
  redis: 6379,
  rabbitmq: 5672
}

// 环境类型 - 精简版
const ENV_TYPES = {
  DEV: 'development',
  PROD: 'production'
}

// =================== 统一的环境配置 ===================
// 【重要】只在这里修改环境配置，其他地方不要重复定义
const CURRENT_ENV = ENV_TYPES.DEV // 设置当前环境

// 获取当前环境配置
const getCurrentEnvironment = () => {
  console.log(`[DOMAINS] 当前环境: ${CURRENT_ENV}`)
  console.log(`[DOMAINS] 环境映射: DEV=${ENV_TYPES.DEV}, PROD=${ENV_TYPES.PROD}`)
  console.log(`[DOMAINS] 当前环境配置:`, DOMAINS[CURRENT_ENV])
  return CURRENT_ENV
}

// 获取当前环境的域名配置
const getCurrentDomainConfig = (env = CURRENT_ENV) => {
  return DOMAINS[env] || DOMAINS[ENV_TYPES.DEV]
}

// 获取API基础URL
const getApiBaseUrl = (env = CURRENT_ENV) => {
  const config = getCurrentDomainConfig(env)
  return config.urls.apiBase
}

// 获取上传域名
const getUploadDomain = (env = CURRENT_ENV) => {
  const config = getCurrentDomainConfig(env)
  return config.urls.uploadDomain
}

// 导出配置
module.exports = {
  DOMAINS,
  PORTS,
  ENV_TYPES,
  CURRENT_ENV,
  getCurrentEnvironment,
  getCurrentDomainConfig,
  getApiBaseUrl,
  getUploadDomain
} 