.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 120rpx;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 40rpx 40rpx 60rpx;
  color: white;
  text-align: center;
}

.header .title {
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.header .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 表单容器 */
.form {
  margin-top: -30rpx;
  padding: 0 20rpx 20rpx;
  position: relative;
  z-index: 1;
}

/* 表单区块 */
.section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.title-required {
  color: #ff4d4f;
  font-size: 28rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 标签样式 */
.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
  display: block;
}

.label.required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4rpx;
}

/* 输入框样式 */
input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

input:focus {
  border-color: #3b82f6;
  background: white;
  outline: none;
}

input::placeholder {
  color: #adb5bd;
  font-size: 28rpx;
}

/* 操作按钮 */
.name-actions {
  margin-top: 12rpx;
}

.action-btn {
  display: inline-block;
  padding: 8rpx 16rpx;
  background: #f0f9eb;
  color: #67c23a;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e1f3d8;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 头像上传样式 */
.avatar-upload {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  background: #f8f9fa;
  border: 3rpx dashed #e0e0e0;
  position: relative;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto 16rpx;
}

.avatar-upload:active {
  transform: scale(0.98);
}

.avatar-upload image {
  width: 100%;
  height: 100%;
}

/* 上传占位符样式 */
.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.upload-placeholder .icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #bbb;
  display: block;
}

.upload-placeholder text {
  display: block;
  text-align: center;
}

/* 身份证上传区域 */
.id-card-upload {
  display: flex;
  gap: 20rpx;
}

.upload-item {
  flex: 1;
  height: 180rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx dashed #e0e0e0;
  position: relative;
  transition: all 0.3s ease;
}

.upload-item:active {
  transform: scale(0.98);
  border-color: #3b82f6;
}

.upload-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 上传状态 */
.uploading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  border-radius: inherit;
  z-index: 10;
}

/* 单选框样式 */
.radio-item {
  display: inline-block;
  margin-right: 32rpx;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #333;
}

.radio-item radio {
  margin-right: 8rpx;
}

/* 选择器样式 */
.picker {
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

.picker-selected {
  background: white;
  border-color: #3b82f6;
  color: #333;
}

/* 文本域样式 */
textarea {
  width: 100%;
  min-height: 200rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

textarea:focus {
  border-color: #3b82f6;
  background: white;
  outline: none;
}

textarea::placeholder {
  color: #adb5bd;
  font-size: 28rpx;
}

/* 字符计数 */
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 提交区域 */
.submit-section {
  padding: 32rpx 20rpx;
  text-align: center;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.submit-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.submit-tip {
  font-size: 24rpx;
  color: #999;
}

/* 推荐人验证状态样式 */
.validation-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.validation-status.validating {
  background: #f0f9ff;
  color: #0369a1;
  border: 1rpx solid #bae6fd;
}

.validation-status.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1rpx solid #fecaca;
}

.validation-status.success {
  background: #f0fdf4;
  color: #16a34a;
  border: 1rpx solid #bbf7d0;
}

.validation-status .icon {
  font-size: 28rpx;
}

.validation-status text {
  line-height: 1.4;
}

.retry-btn {
  margin-left: auto;
  padding: 4rpx 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4rpx;
  font-size: 22rpx !important;
  color: #dc2626 !important;
  border: 1rpx solid #dc2626;
}

.retry-btn:active {
  opacity: 0.7;
}
